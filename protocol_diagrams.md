# High-Level Agent Workflow
```mermaid
flowchart TD
    subgraph Core Autonomous TDD Cycle
        Orchestrator -- "1. Instructs to write tests" --> TestAgent;
        TestAgent -- "2. Creates failing tests" --> Orchestrator;
        Orchestrator -- "3. Instructs to write code" --> CoderAgent;
        CoderAgent -- "4. Writes code to pass tests" --> Orchestrator;
        Orchestrator -- "5. Instructs to verify" --> VerificationAgent;
        
        VerificationAgent -- "6a. Reports PASS" --> Orchestrator;
        VerificationAgent -- "6b. Reports FAIL" --> DebugAgent;
        
        DebugAgent -- "7a. Proposes fix" --> CoderAgent;
        DebugAgent -- "7b. Declares Unrecoverable" --> Orchestrator;
    end
    
    Orchestrator -- "On Pass & Quality Check" --> NextState(Proceed to next FSM state);
    Orchestrator -- "On Unrecoverable Error" --> Recovery(Initiate Advanced Failure Recovery);
```

# High-Level Agent Alternative Workflow
```mermaid
flowchart TD
    subgraph Core Autonomous TDD Cycle
        Orchestrator -- "1. Instructs to write tests" --> TestAgent;
        TestAgent -- "2. Creates failing tests" --> Orchestrator;
        Orchestrator -- "3. Instructs to write code" --> CoderAgent;
        CoderAgent -- "4. Writes code to pass tests" --> Orchestrator;
        Orchestrator -- "5. Instructs to verify" --> VerificationAgent;
        
        VerificationAgent -- "6a. Reports PASS" --> Orchestrator;
        VerificationAgent -- "6b. Reports FAIL" --> DebugAgent;
        
        DebugAgent -- "7a. Proposes fix" --> CoderAgent;
        DebugAgent -- "7b. Declares Unrecoverable" --> Orchestrator;
    end
    
    Orchestrator -- "On Pass & Quality Check" --> NextState(Proceed to next FSM state);
    Orchestrator -- "On Unrecoverable Error" --> Recovery(Initiate Advanced Failure Recovery);
```

# Finite State Machine (FSM) Alternative Operation (Horizontal)
```mermaid
stateDiagram-v2
    direction TB

    [*] --> INITIALIZING
    INITIALIZING --> STARTUP_VERIFICATION
    STARTUP_VERIFICATION --> TASK_HIERARCHY_BUILDING : State Verified
    STARTUP_VERIFICATION --> RECOVERABLE_ERROR : Verification Failed
    TASK_HIERARCHY_BUILDING --> REPLANNING : Hierarchy Built

    REPLANNING --> TEST_WRITING : Next Epic Planned

    state TDD_Cycle {
        direction TB
        TEST_WRITING --> CODING : tests_generated
        CODING --> VERIFYING : code_generated
        CODING --> AWAITING_TEST_REFINEMENT : ambiguous_test_detected
        AWAITING_TEST_REFINEMENT --> CODING : test_refined
        AWAITING_TEST_REFINEMENT --> RECOVERABLE_ERROR : Refinement Failed
        VERIFYING --> DEBUGGING : verification_failed
        DEBUGGING --> CODING : fix_proposed
    }

    DEBUGGING --> REPLANNING : unrecoverable_error

    VERIFYING --> DOCUMENTING : verification_passed
    DOCUMENTING --> SEEDING : documentation_complete
    SEEDING --> PRE_COMMIT_REVIEW : seeding_complete
    PRE_COMMIT_REVIEW --> COMMIT_CHANGES : diff_is_clean
    
    state After_Commit_Choice <<choice>>
    COMMIT_CHANGES --> After_Commit_Choice

    After_Commit_Choice --> CHECK_TERMINALS : [infra_ready = true]
    After_Commit_Choice --> REPLANNING : [infra_ready = false]

    CHECK_TERMINALS --> REPLANNING : All Terminals OK
    CHECK_TERMINALS --> TERMINAL_DEBUGGING : Error Detected
    TERMINAL_DEBUGGING --> TERMINAL_VERIFYING : fix_applied
    TERMINAL_VERIFYING --> CHECK_TERMINALS : Verifying Fix

    state Recovery_Decision <<choice>>
    RECOVERABLE_ERROR --> Recovery_Decision : Orchestrator Autonomy
    Recovery_Decision --> REPLANNING : Decide to Replan
    Recovery_Decision --> DEBUGGING : Decide to Debug
    Recovery_Decision --> HALTED : Decide to Halt

    state End_States {
        direction LR
        REPLANNING --> COMPLETED : All Epics Done
        TERMINAL_DEBUGGING --> HALTED : Retry Limit Exceeded
    }
```
# Finite State Machine (FSM) Operation (Horizontal)

```mermaid
stateDiagram-v2
    direction LR

    [*] --> INITIALIZING
    INITIALIZING --> STARTUP_VERIFICATION
    STARTUP_VERIFICATION --> TASK_HIERARCHY_BUILDING : State Verified
    STARTUP_VERIFICATION --> RECOVERABLE_ERROR : Verification Failed
    TASK_HIERARCHY_BUILDING --> REPLANNING : Hierarchy Built

    REPLANNING --> TEST_WRITING : Next Epic Planned

    state TDD_Cycle {
        direction LR
        TEST_WRITING --> CODING : tests_generated
        CODING --> VERIFYING : code_generated
        CODING --> AWAITING_TEST_REFINEMENT : ambiguous_test_detected
        AWAITING_TEST_REFINEMENT --> CODING : test_refined
        AWAITING_TEST_REFINEMENT --> RECOVERABLE_ERROR : Refinement Failed
        VERIFYING --> DEBUGGING : verification_failed
        DEBUGGING --> CODING : fix_proposed
    }

    DEBUGGING --> REPLANNING : unrecoverable_error

    VERIFYING --> DOCUMENTING : verification_passed
    DOCUMENTING --> SEEDING : documentation_complete
    SEEDING --> PRE_COMMIT_REVIEW : seeding_complete
    PRE_COMMIT_REVIEW --> COMMIT_CHANGES : diff_is_clean
    
    state After_Commit_Choice <<choice>>
    COMMIT_CHANGES --> After_Commit_Choice

    After_Commit_Choice --> CHECK_TERMINALS : [infra_ready = true]
    After_Commit_Choice --> REPLANNING : [infra_ready = false]

    CHECK_TERMINALS --> REPLANNING : All Terminals OK
    CHECK_TERMINALS --> TERMINAL_DEBUGGING : Error Detected
    TERMINAL_DEBUGGING --> TERMINAL_VERIFYING : fix_applied
    TERMINAL_VERIFYING --> CHECK_TERMINALS : Verifying Fix

    state Recovery_Decision <<choice>>
    RECOVERABLE_ERROR --> Recovery_Decision : Orchestrator Autonomy
    Recovery_Decision --> REPLANNING : Decide to Replan
    Recovery_Decision --> DEBUGGING : Decide to Debug
    Recovery_Decision --> HALTED : Decide to Halt

    state End_States {
        REPLANNING --> COMPLETED : All Epics Done
        TERMINAL_DEBUGGING --> HALTED : Retry Limit Exceeded
    }
```


# Finite State Machine (FSM) Alternative Operation (Vertical)
```mermaid
stateDiagram-v2
    direction TB

    [*] --> INITIALIZING
    INITIALIZING --> STARTUP_VERIFICATION
    STARTUP_VERIFICATION --> TASK_HIERARCHY_BUILDING : State Verified
    STARTUP_VERIFICATION --> RECOVERABLE_ERROR : Verification Failed
    TASK_HIERARCHY_BUILDING --> REPLANNING : Hierarchy Built

    REPLANNING --> TEST_WRITING : Next Epic Planned

    state TDD_Cycle {
        direction TB
        TEST_WRITING --> CODING : tests_generated
        CODING --> VERIFYING : code_generated
        CODING --> AWAITING_TEST_REFINEMENT : ambiguous_test_detected
        AWAITING_TEST_REFINEMENT --> CODING : test_refined
        AWAITING_TEST_REFINEMENT --> RECOVERABLE_ERROR : Refinement Failed
        VERIFYING --> DEBUGGING : verification_failed
        DEBUGGING --> CODING : fix_proposed
    }

    DEBUGGING --> REPLANNING : unrecoverable_error

    VERIFYING --> DOCUMENTING : verification_passed
    DOCUMENTING --> SEEDING : documentation_complete
    SEEDING --> PRE_COMMIT_REVIEW : seeding_complete
    PRE_COMMIT_REVIEW --> COMMIT_CHANGES : diff_is_clean
    
    state After_Commit_Choice <<choice>>
    COMMIT_CHANGES --> After_Commit_Choice

    After_Commit_Choice --> CHECK_TERMINALS : [infra_ready = true]
    After_Commit_Choice --> REPLANNING : [infra_ready = false]

    CHECK_TERMINALS --> REPLANNING : All Terminals OK
    CHECK_TERMINALS --> TERMINAL_DEBUGGING : Error Detected
    TERMINAL_DEBUGGING --> TERMINAL_VERIFYING : fix_applied
    TERMINAL_VERIFYING --> CHECK_TERMINALS : Fix Verified & Error Epic Logged

    state Recovery_Decision <<choice>>
    RECOVERABLE_ERROR --> Recovery_Decision : Orchestrator Autonomy
    Recovery_Decision --> REPLANNING : Decide to Replan
    Recovery_Decision --> DEBUGGING : Decide to Debug
    Recovery_Decision --> HALTED : Decide to Halt

    state End_States {
        direction LR
        REPLANNING --> COMPLETED : All Epics Done
        TERMINAL_DEBUGGING --> HALTED : Retry Limit Exceeded
    }
```

# Unified FSM Operation Veritcal
```mermaid
stateDiagram-v2
    direction TB

    [*] --> INITIALIZING
    INITIALIZING --> STARTUP_VERIFICATION
    STARTUP_VERIFICATION --> STRATEGIC_PLANNING
    STRATEGIC_PLANNING --> REPLANNING: Macro-plan created

    note right of INITIALIZING
        FSM_Transition_Meta_Protocol active:
        Any primary state transition is routed through
        CHECK_TERMINALS -> TERMINAL_DEBUGGING (if needed)
        before proceeding.
    end note

    state REPLANNING {
        direction TB
        [*] --> PRE_EPIC_VALIDATION
        PRE_EPIC_VALIDATION --> WORK_SELECTION: if sanity_check_passed
        PRE_EPIC_VALIDATION --> HALTED: if sanity_check_failed
        WORK_SELECTION --> TASK_HIERARCHY_BUILDING
        TASK_HIERARCHY_BUILDING --> [*]
        note right of WORK_SELECTION: Selects next unblocked Epic from task_list.md
        note right of TASK_HIERARCHY_BUILDING: Creates Master Task in integrated tracker
    }

    REPLANNING --> TEST_WRITING: Epic prepared

    state TDD_Cycle {
        direction TB
        TEST_WRITING --> CODING: tests_generated
        CODING --> VERIFYING: code_generated
        CODING --> AWAITING_TEST_REFINEMENT: ambiguous_test_detected
        AWAITING_TEST_REFINEMENT --> CODING: test_refined
    }

    VERIFYING --> DEBUGGING: verification_failed
    DEBUGGING --> VERIFYING: fix_applied
    DEBUGGING --> ADVANCED_FAILURE_RECOVERY: budget_exhausted
    ADVANCED_FAILURE_RECOVERY --> REPLANNING: new_error_epic_created

    VERIFYING --> DOCUMENTING: verification_passed
    DOCUMENTING --> SEEDING
    SEEDING --> PRE_COMMIT_REVIEW
    PRE_COMMIT_REVIEW --> COMMIT_CHANGES: changes_cleaned

    COMMIT_CHANGES --> TDD_Cycle: if more_tasks_in_epic
    COMMIT_CHANGES --> POST_COMPLETION_MICRO_AUDIT: if epic_complete

    POST_COMPLETION_MICRO_AUDIT --> REPLANNING: audit_passed
    POST_COMPLETION_MICRO_AUDIT --> DEBUGGING: audit_failed

    REPLANNING --> COMPLETED: if no_more_epics
```

# Unified FSM Operation Horizontal

```mermaid
stateDiagram-v2
    direction LR

    [*] --> INITIALIZING
    INITIALIZING --> STARTUP_VERIFICATION
    STARTUP_VERIFICATION --> STRATEGIC_PLANNING
    STRATEGIC_PLANNING --> REPLANNING: Macro-plan created

    note right of INITIALIZING
        FSM_Transition_Meta_Protocol active:
        Any primary state transition is routed through
        CHECK_TERMINALS -> TERMINAL_DEBUGGING (if needed)
        before proceeding.
    end note

    state REPLANNING {
        direction LR
        [*] --> PRE_EPIC_VALIDATION
        PRE_EPIC_VALIDATION --> WORK_SELECTION: if sanity_check_passed
        PRE_EPIC_VALIDATION --> HALTED: if sanity_check_failed
        WORK_SELECTION --> TASK_HIERARCHY_BUILDING
        TASK_HIERARCHY_BUILDING --> [*]
        note right of WORK_SELECTION: Selects next unblocked Epic from task_list.md
        note right of TASK_HIERARCHY_BUILDING: Creates Master Task in integrated tracker
    }

    REPLANNING --> TEST_WRITING: Epic prepared

    state TDD_Cycle {
        direction TB
        TEST_WRITING --> CODING: tests_generated
        CODING --> VERIFYING: code_generated
        CODING --> AWAITING_TEST_REFINEMENT: ambiguous_test_detected
        AWAITING_TEST_REFINEMENT --> CODING: test_refined
    }

    VERIFYING --> DEBUGGING: verification_failed
    DEBUGGING --> VERIFYING: fix_applied
    DEBUGGING --> ADVANCED_FAILURE_RECOVERY: budget_exhausted
    ADVANCED_FAILURE_RECOVERY --> REPLANNING: new_error_epic_created

    VERIFYING --> DOCUMENTING: verification_passed
    DOCUMENTING --> SEEDING
    SEEDING --> PRE_COMMIT_REVIEW
    PRE_COMMIT_REVIEW --> COMMIT_CHANGES: changes_cleaned

    COMMIT_CHANGES --> TDD_Cycle: if more_tasks_in_epic
    COMMIT_CHANGES --> POST_COMPLETION_MICRO_AUDIT: if epic_complete

    POST_COMPLETION_MICRO_AUDIT --> REPLANNING: audit_passed
    POST_COMPLETION_MICRO_AUDIT --> DEBUGGING: audit_failed

    REPLANNING --> COMPLETED: if no_more_epics
```

# Finite State Machine (FSM) Operation (Vertical)

```mermaid
stateDiagram-v2
    direction TB

    [*] --> INITIALIZING
    INITIALIZING --> STARTUP_VERIFICATION
    STARTUP_VERIFICATION --> TASK_HIERARCHY_BUILDING : State Verified
    STARTUP_VERIFICATION --> RECOVERABLE_ERROR : Verification Failed
    TASK_HIERARCHY_BUILDING --> REPLANNING : Hierarchy Built

    REPLANNING --> TEST_WRITING : Next Epic Planned

    state TDD_Cycle {
        direction TB
        TEST_WRITING --> CODING : tests_generated
        CODING --> VERIFYING : code_generated
        CODING --> AWAITING_TEST_REFINEMENT : ambiguous_test_detected
        AWAITING_TEST_REFINEMENT --> CODING : test_refined
        AWAITING_TEST_REFINEMENT --> RECOVERABLE_ERROR : Refinement Failed
        VERIFYING --> DEBUGGING : verification_failed
        DEBUGGING --> CODING : fix_proposed
    }

    DEBUGGING --> REPLANNING : unrecoverable_error

    VERIFYING --> DOCUMENTING : verification_passed
    DOCUMENTING --> SEEDING : documentation_complete
    SEEDING --> PRE_COMMIT_REVIEW : seeding_complete
    PRE_COMMIT_REVIEW --> COMMIT_CHANGES : diff_is_clean
    
    state After_Commit_Choice <<choice>>
    COMMIT_CHANGES --> After_Commit_Choice

    After_Commit_Choice --> CHECK_TERMINALS : [infra_ready = true]
    After_Commit_Choice --> REPLANNING : [infra_ready = false]

    CHECK_TERMINALS --> REPLANNING : All Terminals OK
    CHECK_TERMINALS --> TERMINAL_DEBUGGING : Error Detected
    TERMINAL_DEBUGGING --> TERMINAL_VERIFYING : fix_applied
    TERMINAL_VERIFYING --> CHECK_TERMINALS : Verifying Fix

    state Recovery_Decision <<choice>>
    RECOVERABLE_ERROR --> Recovery_Decision : Orchestrator Autonomy
    Recovery_Decision --> REPLANNING : Decide to Replan
    Recovery_Decision --> DEBUGGING : Decide to Debug
    Recovery_Decision --> HALTED : Decide to Halt

    state End_States {
        direction LR
        REPLANNING --> COMPLETED : All Epics Done
        TERMINAL_DEBUGGING --> HALTED : Retry Limit Exceeded
    }
```


# Audit & Refactoring FSM

```mermaid
graph TD
    %% Styling Definitions
    classDef process fill:#E3F2FD,stroke:#333,stroke-width:2px;
    classDef decision fill:#FFF9C4,stroke:#F57F17,stroke-width:2px;
    classDef human fill:#FFCDD2,stroke:#B71C1C,stroke-width:2px;
    classDef startEnd fill:#E8F5E9,stroke:#2E7D32,stroke-width:2px;
    classDef io fill:#F3E5F5,stroke:#4A148C,stroke-width:2px;

    %% FSM States
    A(Initialize Session<br/>Check for resumable state) --> B["<strong>1. PERFORM_DEEP_HEALTH_CHECK</strong><br/>- Run All Tests<br/>- Analyze Test Coverage<br/>- Review Test Applicability<br/>- Check Data Integrity<br/><em>Agent: VerificationAgent</em>"];
    B --> C["<strong>2. INTERNAL_AUDIT</strong><br/>Analyze codebase against<br/>all project documentation<br/><em>Agent: ArchitectAgent</em>"];
    C --> D["<strong>3. VERIFY_AUDIT_FINDINGS</strong><br/>Write new, failing tests<br/>to provide objective proof<br/>of discovered bugs<br/><em>Agent: TestAgent</em>"];
    D --> E["<strong>4. TRIAGE & APPLY_IMMEDIATE_FIXES</strong><br/>Autonomously fix simple,<br/>low-risk issues (e.g., styling)<br/><em>Agents: ArchitectAgent, CoderAgent</em>"];
    E --> F["<strong>5. PARITY_ANALYSIS</strong><br/>Perform deep semantic<br/>comparison against reference code<br/><em>Agent: ArchitectAgent</em>"];
    F --> G["<strong>6. GENERATE_STRATEGIC_AUDIT_REPORT</strong><br/>Consolidate ALL findings and<br/>propose strategic solutions<br/><em>Agent: ArchitectAgent</em>"];
    G --> H{<strong>7. AWAITING_STRATEGY_APPROVAL</strong><br/>Request single, bulk approval<br/>for all subsequent changes<br/><em>Agent: HumanInteractionAgent</em>};
    
    H -- Yes --> I["<strong>8. TRANSCRIBE_REPORT_TO_EPICS</strong><br/>Parse final report and<br/>generate new EPICs in memory"];
    I --> J["<strong>9. UPDATING_TASK_LIST</strong><br/>Write all new EPICs to<br/>the task_list.md file"];
    J --> K["<strong>10. RESTRUCTURE_TASK_LIST_FILE</strong><br/>Rewrite the entire task_list.md<br/>into a clean, cohesive structure"];
    K --> L(<strong>11. COMPLETED</strong><br/>Session successful);

    H -- No --> M(<strong>12. HALTED</strong><br/>Audit report is saved.<br/>No changes are applied.);

    %% Apply Classes to Nodes
    class A,L,M startEnd;
    class B,C,D,E,F,G,I,J,K process;
    class H decision;
```


---
# Task List Restructuring Protocol
---
```mermaid
graph LR
    %% Styling
    classDef category stroke:#01579B,stroke-width:1px,color:#000;
    classDef process stroke:#F57F17,stroke-width:2px;

    subgraph Before Restructuring
        direction LR
        A["<strong>Original task_list.md</strong><br/>- EPIC-03 (Completed)<br/>- EPIC-AD-HOC-06 (Completed)<br/>- EPIC-04 (In_Progress)<br/>- EPIC-AUDIT-002 (New)<br/>- EPIC-05 (Pending)<br/>- EPIC-PARITY-FEAT-008 (New)<br/>- EPIC-AD-HOC-05 (In_Progress)"];
    end

    subgraph After Restructuring
        direction TB
        C["<strong>Core Application Roadmap (Active & Pending)</strong><br/>- EPIC-04 (In_Progress)<br/>- EPIC-05 (Pending)"]
        D["<strong>High-Priority Corrective Epics (Generated by Audit)</strong><br/>- EPIC-AUDIT-002 (New)<br/>- EPIC-PARITY-FEAT-008 (New)"]
        E["<strong>Completed Work</strong><br/><br/><u>Core Roadmap Milestones:</u><br/>- EPIC-03 (Completed)<br/><br/><u>Ad-Hoc & Critical Interventions:</u><br/>- EPIC-AD-HOC-06 (Completed)"]
        F["<strong>Actionable Task List</strong><br/>- EPIC-AD-HOC-05 (In_Progress)"]
    end
    
    A -- "RESTRUCTURE_TASK_LIST_FILE state" --> B(( ))

    B -- "Categorizes Epics by<br/>Type and Status" --> C
    B --> D
    B --> E
    B --> F
    
    class C,D,E,F category;
    class B process;
```