"""
Advanced Search Algorithm Tests

Tests for the advanced search algorithm with fuzzy matching capabilities.
This test suite covers:
- Fuzzy string matching for typos and partial matches
- Search result ranking and relevance scoring
- Performance optimization for large datasets
- Edge cases and error handling

Following TDD protocol - these tests should initially fail until implementation is complete.
"""

import pytest
import random
from django.test import TestCase
from django.db.models import Q
from unittest.mock import patch, MagicMock

from catalog.models import Service, ServiceProvider, ServiceCategory
from catalog.search_algorithms import (
    AdvancedSearchAlgorithm,
    FuzzyMatcher,
    SearchResultRanker,
    SearchIndexer
)
from catalog.factories import ServiceFactory, ProviderFactory, ServiceCategoryFactory


class TestFuzzyMatcher(TestCase):
    """Test fuzzy string matching functionality"""
    
    def setUp(self):
        self.fuzzy_matcher = FuzzyMatcher()
    
    def test_exact_match_returns_high_score(self):
        """Test that exact matches return maximum relevance score"""
        query = "hair styling"
        text = "hair styling"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertAlmostEqual(score, 1.0, places=2)
    
    def test_partial_match_returns_medium_score(self):
        """Test that partial matches return appropriate scores"""
        query = "hair"
        text = "hair styling and coloring"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertGreater(score, 0.5)
        self.assertLess(score, 1.0)
    
    def test_typo_tolerance_single_character(self):
        """Test that single character typos are handled gracefully"""
        query = "hiar styling"  # typo: hiar instead of hair
        text = "hair styling"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertGreater(score, 0.3)  # Should still be relevant despite typo
    
    def test_typo_tolerance_multiple_characters(self):
        """Test handling of multiple character typos"""
        query = "hiar stylingg"  # multiple typos
        text = "hair styling"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertGreater(score, 0.1)  # Should still have some relevance
    
    def test_case_insensitive_matching(self):
        """Test that matching is case insensitive"""
        query = "HAIR STYLING"
        text = "hair styling"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertEqual(score, 1.0)
    
    def test_word_order_flexibility(self):
        """Test that word order doesn't significantly impact relevance"""
        query = "styling hair"
        text = "hair styling"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertGreater(score, 0.4)  # Should still be relevant with different word order
    
    def test_no_match_returns_zero_score(self):
        """Test that completely unrelated text returns zero score"""
        query = "hair styling"
        text = "car repair"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertLess(score, 0.1)  # Should be very low for unrelated terms
    
    def test_empty_query_handling(self):
        """Test handling of empty queries"""
        query = ""
        text = "hair styling"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertEqual(score, 0.0)
    
    def test_empty_text_handling(self):
        """Test handling of empty text"""
        query = "hair styling"
        text = ""
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertEqual(score, 0.0)


class TestSearchIndexer(TestCase):
    """Test search indexing functionality"""

    @staticmethod
    def random_choice(choices):
        """Get random choice from a list"""
        return random.choice(choices)

    def setUp(self):
        self.indexer = SearchIndexer()
        
        # Create test data
        self.category = ServiceCategoryFactory.create_category(name="Hair & Beauty")
        self.provider = ProviderFactory.create_provider(
            business_name="Elite Hair Salon",
            business_description="Professional hair styling services"
        )
        self.service = ServiceFactory.create_service(
            provider=self.provider,
            name="Hair Styling",
            description="Professional hair cutting and styling",
            category=self.category
        )
    
    def test_service_indexing_creates_searchable_fields(self):
        """Test that services are properly indexed with searchable fields"""
        indexed_service = self.indexer.index_service(self.service)
        
        self.assertIn('searchable_text', indexed_service)
        self.assertIn('hair styling', indexed_service['searchable_text'].lower())
        self.assertIn('elite hair salon', indexed_service['searchable_text'].lower())
        self.assertIn('hair & beauty', indexed_service['searchable_text'].lower())
    
    def test_search_index_includes_all_relevant_fields(self):
        """Test that search index includes all relevant searchable fields"""
        indexed_service = self.indexer.index_service(self.service)
        searchable_text = indexed_service['searchable_text'].lower()
        
        # Should include service name
        self.assertIn(self.service.name.lower(), searchable_text)
        # Should include service description
        self.assertIn(self.service.description.lower(), searchable_text)
        # Should include provider name
        self.assertIn(self.provider.business_name.lower(), searchable_text)
        # Should include category name
        self.assertIn(self.category.name.lower(), searchable_text)
    
    def test_index_performance_with_large_dataset(self):
        """Test indexing performance with large number of services"""
        # Create 100 test services
        # First create categories and providers
        categories = ServiceCategoryFactory.create_batch(5)
        providers = []
        for i in range(20):
            provider = ProviderFactory.create_provider()
            providers.append(provider)

        services = []
        for i in range(100):
            provider = self.random_choice(providers)
            service = ServiceFactory.create_service(provider=provider)
            services.append(service)
        
        import time
        start_time = time.time()
        
        indexed_services = []
        for service in services:
            indexed_services.append(self.indexer.index_service(service))
        
        end_time = time.time()
        indexing_time = end_time - start_time
        
        # Should index 100 services in less than 1 second
        self.assertLess(indexing_time, 1.0)
        self.assertEqual(len(indexed_services), 100)


class TestSearchResultRanker(TestCase):
    """Test search result ranking functionality"""
    
    def setUp(self):
        self.ranker = SearchResultRanker()
        
        # Create test services with different characteristics
        self.category = ServiceCategoryFactory.create_category(name="Hair & Beauty")
        self.provider1 = ProviderFactory.create_provider()
        self.provider2 = ProviderFactory.create_provider()
        self.provider3 = ProviderFactory.create_provider(is_featured=True)

        self.popular_service = ServiceFactory.create_service(
            provider=self.provider1,
            name="Popular Hair Salon",
            is_popular=True,
            booking_count=100
        )
        self.new_service = ServiceFactory.create_service(
            provider=self.provider2,
            name="New Hair Studio",
            is_popular=False,
            booking_count=5
        )
        self.featured_service = ServiceFactory.create_service(
            provider=self.provider3,
            name="Featured Beauty Spa",
            is_popular=True,
            booking_count=50
        )
    
    def test_exact_match_gets_highest_rank(self):
        """Test that exact matches receive highest ranking"""
        query = "Popular Hair Salon"
        services = [self.popular_service, self.new_service, self.featured_service]
        
        ranked_results = self.ranker.rank_results(query, services)
        
        # Popular service should be first due to exact match
        self.assertEqual(ranked_results[0]['service'].id, self.popular_service.id)
        self.assertGreater(ranked_results[0]['relevance_score'], 0.7)
    
    def test_popularity_affects_ranking(self):
        """Test that service popularity affects ranking"""
        query = "hair"  # Matches multiple services
        services = [self.new_service, self.popular_service]
        
        ranked_results = self.ranker.rank_results(query, services)
        
        # Popular service should rank higher
        self.assertEqual(ranked_results[0]['service'].id, self.popular_service.id)
    
    def test_featured_services_get_boost(self):
        """Test that featured services get ranking boost"""
        query = "beauty"
        services = [self.new_service, self.featured_service]
        
        ranked_results = self.ranker.rank_results(query, services)
        
        # Featured service should rank higher
        self.assertEqual(ranked_results[0]['service'].id, self.featured_service.id)
    
    def test_ranking_score_calculation(self):
        """Test that ranking scores are calculated correctly"""
        query = "hair"
        services = [self.popular_service]
        
        ranked_results = self.ranker.rank_results(query, services)
        result = ranked_results[0]
        
        # Should have all ranking components
        self.assertIn('relevance_score', result)
        self.assertIn('popularity_score', result)
        self.assertIn('final_score', result)
        
        # Scores should be between 0 and 1
        self.assertGreaterEqual(result['relevance_score'], 0.0)
        self.assertLessEqual(result['relevance_score'], 1.0)
        self.assertGreaterEqual(result['final_score'], 0.0)
        self.assertLessEqual(result['final_score'], 1.0)


class TestAdvancedSearchAlgorithm(TestCase):
    """Test the complete advanced search algorithm"""

    @staticmethod
    def random_choice(choices):
        """Get random choice from a list"""
        return random.choice(choices)

    def setUp(self):
        self.search_algorithm = AdvancedSearchAlgorithm()
        
        # Create comprehensive test data
        self.hair_category = ServiceCategoryFactory.create_category(name="Hair & Beauty")
        self.spa_category = ServiceCategoryFactory.create_category(name="Spa & Wellness")

        self.hair_provider = ProviderFactory.create_provider(
            business_name="Elite Hair Salon",
            city="Toronto"
        )
        self.spa_provider = ProviderFactory.create_provider(
            business_name="Zen Spa Retreat",
            city="Toronto"
        )

        self.hair_service = ServiceFactory.create_service(
            provider=self.hair_provider,
            name="Hair Styling and Cut",
            description="Professional hair cutting and styling services",
            category=self.hair_category,
            is_popular=True
        )
        self.spa_service = ServiceFactory.create_service(
            provider=self.spa_provider,
            name="Relaxing Massage",
            description="Full body relaxation massage therapy",
            category=self.spa_category,
            is_popular=True
        )
    
    def test_search_with_exact_match(self):
        """Test search with exact service name match"""
        results = self.search_algorithm.search("Hair Styling and Cut")

        self.assertGreater(len(results), 0)
        self.assertEqual(results[0]['service'].id, self.hair_service.id)
        self.assertGreater(results[0]['relevance_score'], 0.8)
    
    def test_search_with_typos(self):
        """Test search handles typos gracefully"""
        results = self.search_algorithm.search("Hair Stylingg")  # typo: extra 'g'

        self.assertGreater(len(results), 0)
        # Should still find the hair service despite typos
        found_hair_service = any(
            result['service'].id == self.hair_service.id
            for result in results
        )
        self.assertTrue(found_hair_service)
    
    def test_search_with_partial_match(self):
        """Test search with partial matches"""
        results = self.search_algorithm.search("hair")
        
        self.assertGreater(len(results), 0)
        # Should find hair-related services
        found_hair_service = any(
            result['service'].id == self.hair_service.id 
            for result in results
        )
        self.assertTrue(found_hair_service)
    
    def test_search_by_provider_name(self):
        """Test search by provider business name"""
        results = self.search_algorithm.search("Elite Hair Salon")
        
        self.assertGreater(len(results), 0)
        self.assertEqual(results[0]['service'].provider.id, self.hair_provider.id)
    
    def test_search_by_category(self):
        """Test search by category name"""
        results = self.search_algorithm.search("Spa & Wellness")
        
        self.assertGreater(len(results), 0)
        found_spa_service = any(
            result['service'].id == self.spa_service.id 
            for result in results
        )
        self.assertTrue(found_spa_service)
    
    def test_empty_query_returns_empty_results(self):
        """Test that empty queries return empty results"""
        results = self.search_algorithm.search("")
        self.assertEqual(len(results), 0)
    
    def test_no_match_returns_empty_results(self):
        """Test that queries with no matches return empty results"""
        results = self.search_algorithm.search("nonexistent service xyz")
        self.assertEqual(len(results), 0)
    
    def test_search_performance_with_large_dataset(self):
        """Test search performance with large number of services"""
        # Create 200 additional services
        categories = ServiceCategoryFactory.create_batch(5)
        providers = []
        for i in range(40):
            provider = ProviderFactory.create_provider()
            providers.append(provider)

        for i in range(200):
            provider = self.random_choice(providers)
            ServiceFactory.create_service(provider=provider)
        
        import time
        start_time = time.time()
        
        results = self.search_algorithm.search("hair styling")
        
        end_time = time.time()
        search_time = end_time - start_time
        
        # Search should complete in less than 0.5 seconds
        self.assertLess(search_time, 0.5)
        self.assertGreaterEqual(len(results), 0)
    
    def test_search_result_structure(self):
        """Test that search results have correct structure"""
        results = self.search_algorithm.search("hair")
        
        if len(results) > 0:
            result = results[0]
            
            # Should have required fields
            self.assertIn('service', result)
            self.assertIn('relevance_score', result)
            self.assertIn('final_score', result)
            
            # Service should be a Service instance
            self.assertIsInstance(result['service'], Service)
            
            # Scores should be numeric
            self.assertIsInstance(result['relevance_score'], (int, float))
            self.assertIsInstance(result['final_score'], (int, float))


class TestSearchAlgorithmIntegration(TestCase):
    """Integration tests for the complete search system"""
    
    def setUp(self):
        # Create realistic test data
        self.beauty_category = ServiceCategoryFactory.create_category(name="Beauty & Cosmetics")
        self.wellness_category = ServiceCategoryFactory.create_category(name="Health & Wellness")

        # Create providers
        self.salon_provider = ProviderFactory.create_provider(
            business_name="Glamour Beauty Salon",
            business_description="Full service beauty salon",
            city="Toronto"
        )
        self.clinic_provider = ProviderFactory.create_provider(
            business_name="Wellness Health Clinic",
            business_description="Holistic health and wellness services",
            city="Toronto"
        )

        # Create services
        self.manicure_service = ServiceFactory.create_service(
            provider=self.salon_provider,
            name="Professional Manicure",
            description="Complete nail care and manicure service",
            category=self.beauty_category,
            base_price=45.00,
            is_popular=True
        )
        self.massage_service = ServiceFactory.create_service(
            provider=self.clinic_provider,
            name="Therapeutic Massage",
            description="Deep tissue therapeutic massage for wellness",
            category=self.wellness_category,
            base_price=80.00,
            is_popular=True
        )
    
    def test_end_to_end_search_workflow(self):
        """Test complete search workflow from query to ranked results"""
        search_algorithm = AdvancedSearchAlgorithm()
        
        # Test various search scenarios
        test_cases = [
            ("manicure", self.manicure_service),
            ("nail care", self.manicure_service),
            ("massage", self.massage_service),
            ("therapeutic", self.massage_service),
            ("Glamour", self.manicure_service),  # Provider name
            ("Beauty", self.manicure_service),   # Category name
        ]
        
        for query, expected_service in test_cases:
            with self.subTest(query=query):
                results = search_algorithm.search(query)
                
                self.assertGreater(len(results), 0, f"No results for query: {query}")
                
                # Check if expected service is in results
                found_service = any(
                    result['service'].id == expected_service.id 
                    for result in results
                )
                self.assertTrue(found_service, f"Expected service not found for query: {query}")
    
    def test_search_ranking_consistency(self):
        """Test that search ranking is consistent across multiple runs"""
        search_algorithm = AdvancedSearchAlgorithm()
        query = "beauty"
        
        # Run search multiple times
        results_1 = search_algorithm.search(query)
        results_2 = search_algorithm.search(query)
        results_3 = search_algorithm.search(query)
        
        # Results should be consistent
        self.assertEqual(len(results_1), len(results_2))
        self.assertEqual(len(results_2), len(results_3))
        
        if len(results_1) > 0:
            # Top result should be the same
            self.assertEqual(
                results_1[0]['service'].id,
                results_2[0]['service'].id
            )
            self.assertEqual(
                results_2[0]['service'].id,
                results_3[0]['service'].id
            )
