"""
Advanced Search Algorithms for Vierla Service Marketplace

This module implements sophisticated search algorithms with fuzzy matching,
intelligent ranking, and performance optimization for service discovery.

Features:
- Fuzzy string matching with typo tolerance
- Multi-field search indexing
- Relevance-based result ranking
- Performance optimization for large datasets
- Configurable search parameters

Author: Vierla Development Team
Version: 1.0.0
"""

import re
import math
import time
from typing import List, Dict, Any, Optional, Tuple
from difflib import SequenceMatcher
from django.db.models import Q, QuerySet
from django.utils.text import slugify

from .models import Service, ServiceProvider, ServiceCategory


class FuzzyMatcher:
    """
    Advanced fuzzy string matching with typo tolerance and semantic understanding
    """
    
    def __init__(self, typo_tolerance: float = 0.8, word_order_weight: float = 0.3):
        self.typo_tolerance = typo_tolerance
        self.word_order_weight = word_order_weight
        
        # Common word variations and synonyms
        self.synonyms = {
            'hair': ['hairstyle', 'haircut', 'coiffure'],
            'beauty': ['cosmetic', 'aesthetic', 'glamour'],
            'massage': ['therapy', 'treatment', 'bodywork'],
            'nail': ['manicure', 'pedicure', 'nails'],
            'spa': ['wellness', 'relaxation', 'retreat'],
        }
    
    def calculate_similarity(self, query: str, text: str) -> float:
        """
        Calculate similarity score between query and text using multiple algorithms
        
        Args:
            query: Search query string
            text: Text to compare against
            
        Returns:
            Similarity score between 0.0 and 1.0
        """
        if not query or not text:
            return 0.0
        
        # Normalize strings
        query_normalized = self._normalize_text(query)
        text_normalized = self._normalize_text(text)
        
        if not query_normalized or not text_normalized:
            return 0.0
        
        # Calculate different similarity metrics
        exact_score = self._exact_match_score(query_normalized, text_normalized)
        fuzzy_score = self._fuzzy_match_score(query_normalized, text_normalized)
        word_score = self._word_match_score(query_normalized, text_normalized)
        semantic_score = self._semantic_match_score(query_normalized, text_normalized)
        
        # Combine scores with weights - prioritize exact and word matches
        final_score = (
            exact_score * 0.5 +
            word_score * 0.3 +
            fuzzy_score * 0.15 +
            semantic_score * 0.05
        )
        
        return min(final_score, 1.0)
    
    def _normalize_text(self, text: str) -> str:
        """Normalize text for comparison"""
        if not text:
            return ""
        
        # Convert to lowercase and remove extra whitespace
        normalized = re.sub(r'\s+', ' ', text.lower().strip())
        
        # Remove special characters but keep spaces
        normalized = re.sub(r'[^\w\s]', '', normalized)
        
        return normalized
    
    def _exact_match_score(self, query: str, text: str) -> float:
        """Calculate exact match score"""
        if query == text:
            return 1.0
        elif query in text:
            # Higher score for exact substring matches
            return 0.9
        elif text in query:
            return 0.8
        else:
            return 0.0
    
    def _fuzzy_match_score(self, query: str, text: str) -> float:
        """Calculate fuzzy match score using sequence matching"""
        return SequenceMatcher(None, query, text).ratio()
    
    def _word_match_score(self, query: str, text: str) -> float:
        """Calculate word-level match score"""
        query_words = set(query.split())
        text_words = set(text.split())

        if not query_words:
            return 0.0

        # Calculate intersection
        common_words = query_words.intersection(text_words)

        # Calculate score based on word overlap
        word_overlap_score = len(common_words) / len(query_words)

        # Bonus for word order preservation
        if self._check_word_order(query.split(), text.split()):
            word_overlap_score += self.word_order_weight

        # Additional bonus for complete word matches
        if len(common_words) == len(query_words):
            word_overlap_score += 0.2

        return min(word_overlap_score, 1.0)
    
    def _semantic_match_score(self, query: str, text: str) -> float:
        """Calculate semantic match score using synonyms"""
        query_words = query.split()
        text_words = text.split()

        semantic_matches = 0
        total_query_words = len(query_words)

        if total_query_words == 0:
            return 0.0

        # Check if any words are completely unrelated
        unrelated_domains = {
            'hair': ['car', 'repair', 'automotive', 'engine', 'vehicle'],
            'beauty': ['car', 'repair', 'automotive', 'engine', 'vehicle'],
            'spa': ['car', 'repair', 'automotive', 'engine', 'vehicle'],
            'massage': ['car', 'repair', 'automotive', 'engine', 'vehicle'],
            'car': ['hair', 'beauty', 'spa', 'massage', 'nail', 'facial'],
            'repair': ['hair', 'beauty', 'spa', 'massage', 'nail', 'facial']
        }

        # Check for unrelated domain mismatch
        for query_word in query_words:
            if query_word in unrelated_domains:
                for text_word in text_words:
                    if text_word in unrelated_domains[query_word]:
                        return 0.0  # Completely unrelated domains

        for query_word in query_words:
            # Check direct match
            if query_word in text_words:
                semantic_matches += 1
                continue

            # Check synonym matches
            if query_word in self.synonyms:
                for synonym in self.synonyms[query_word]:
                    if synonym in text_words:
                        semantic_matches += 0.8  # Slightly lower score for synonyms
                        break

        return semantic_matches / total_query_words
    
    def _check_word_order(self, query_words: List[str], text_words: List[str]) -> bool:
        """Check if word order is preserved"""
        query_indices = []
        
        for query_word in query_words:
            try:
                index = text_words.index(query_word)
                query_indices.append(index)
            except ValueError:
                return False
        
        # Check if indices are in ascending order
        return query_indices == sorted(query_indices)


class SearchIndexer:
    """
    Service search indexing for optimized search performance
    """
    
    def __init__(self):
        self.indexed_fields = [
            'name',
            'description',
            'provider__business_name',
            'provider__business_description',
            'category__name',
            'provider__city',
        ]
    
    def index_service(self, service: Service) -> Dict[str, Any]:
        """
        Create searchable index for a service
        
        Args:
            service: Service instance to index
            
        Returns:
            Dictionary containing indexed service data
        """
        searchable_text_parts = []
        
        # Add service fields
        if service.name:
            searchable_text_parts.append(service.name)
        if service.description:
            searchable_text_parts.append(service.description)
        
        # Add provider fields
        if service.provider:
            if service.provider.business_name:
                searchable_text_parts.append(service.provider.business_name)
            if service.provider.business_description:
                searchable_text_parts.append(service.provider.business_description)
            if service.provider.city:
                searchable_text_parts.append(service.provider.city)
        
        # Add category fields
        if service.category:
            if service.category.name:
                searchable_text_parts.append(service.category.name)
        
        # Combine all searchable text
        searchable_text = ' '.join(searchable_text_parts)
        
        return {
            'service_id': service.id,
            'searchable_text': searchable_text,
            'service': service,
            'boost_factors': self._calculate_boost_factors(service)
        }
    
    def _calculate_boost_factors(self, service: Service) -> Dict[str, float]:
        """Calculate boost factors for ranking"""
        boost_factors = {
            'popularity': 1.0,
            'featured': 1.0,
            'rating': 1.0,
            'recency': 1.0
        }
        
        # Popularity boost
        if hasattr(service, 'is_popular') and service.is_popular:
            boost_factors['popularity'] = 1.5
        
        # Featured boost (using provider's is_featured)
        if hasattr(service.provider, 'is_featured') and service.provider.is_featured:
            boost_factors['featured'] = 1.3
        
        # Booking count boost
        if hasattr(service, 'booking_count') and service.booking_count:
            if service.booking_count > 50:
                boost_factors['popularity'] *= 1.2
            elif service.booking_count > 20:
                boost_factors['popularity'] *= 1.1
        
        return boost_factors


class SearchResultRanker:
    """
    Advanced search result ranking with multiple factors
    """
    
    def __init__(self):
        self.fuzzy_matcher = FuzzyMatcher()
        
        # Ranking weights
        self.weights = {
            'relevance': 0.5,
            'popularity': 0.2,
            'featured': 0.15,
            'rating': 0.1,
            'recency': 0.05
        }
    
    def rank_results(self, query: str, services: List[Service]) -> List[Dict[str, Any]]:
        """
        Rank search results based on multiple factors
        
        Args:
            query: Original search query
            services: List of services to rank
            
        Returns:
            List of ranked results with scores
        """
        ranked_results = []
        
        for service in services:
            result = self._calculate_service_score(query, service)
            ranked_results.append(result)
        
        # Sort by final score (descending)
        ranked_results.sort(key=lambda x: x['final_score'], reverse=True)
        
        return ranked_results
    
    def _calculate_service_score(self, query: str, service: Service) -> Dict[str, Any]:
        """Calculate comprehensive score for a service"""
        indexer = SearchIndexer()
        indexed_service = indexer.index_service(service)
        
        # Calculate relevance score
        relevance_score = self.fuzzy_matcher.calculate_similarity(
            query, 
            indexed_service['searchable_text']
        )
        
        # Calculate popularity score
        popularity_score = self._calculate_popularity_score(service)
        
        # Calculate featured score (using provider's is_featured)
        featured_score = 1.0 if (hasattr(service.provider, 'is_featured') and service.provider.is_featured) else 0.0
        
        # Calculate rating score (placeholder - would use actual ratings)
        rating_score = 0.8  # Default rating score
        
        # Calculate recency score (placeholder - would use creation/update dates)
        recency_score = 0.7  # Default recency score
        
        # Apply boost factors
        boost_factors = indexed_service['boost_factors']
        
        # Calculate final weighted score
        final_score = (
            relevance_score * self.weights['relevance'] * boost_factors['popularity'] +
            popularity_score * self.weights['popularity'] +
            featured_score * self.weights['featured'] * boost_factors['featured'] +
            rating_score * self.weights['rating'] * boost_factors['rating'] +
            recency_score * self.weights['recency'] * boost_factors['recency']
        )
        
        return {
            'service': service,
            'relevance_score': relevance_score,
            'popularity_score': popularity_score,
            'featured_score': featured_score,
            'rating_score': rating_score,
            'recency_score': recency_score,
            'final_score': min(final_score, 1.0),
            'boost_factors': boost_factors
        }
    
    def _calculate_popularity_score(self, service: Service) -> float:
        """Calculate popularity score based on service metrics"""
        score = 0.5  # Base score
        
        # Boost for popular services
        if hasattr(service, 'is_popular') and service.is_popular:
            score += 0.3
        
        # Boost based on booking count
        if hasattr(service, 'booking_count') and service.booking_count:
            if service.booking_count > 100:
                score += 0.2
            elif service.booking_count > 50:
                score += 0.15
            elif service.booking_count > 20:
                score += 0.1
            elif service.booking_count > 10:
                score += 0.05
        
        return min(score, 1.0)


class AdvancedSearchAlgorithm:
    """
    Main search algorithm combining fuzzy matching, indexing, and ranking
    """
    
    def __init__(self):
        self.fuzzy_matcher = FuzzyMatcher()
        self.indexer = SearchIndexer()
        self.ranker = SearchResultRanker()
        
        # Performance settings
        self.max_results = 100
        self.min_query_length = 1
    
    def search(self, query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Perform advanced search with fuzzy matching and intelligent ranking
        
        Args:
            query: Search query string
            filters: Optional search filters
            
        Returns:
            List of ranked search results
        """
        if not query or len(query.strip()) < self.min_query_length:
            return []
        
        query = query.strip()
        
        # Get candidate services
        candidate_services = self._get_candidate_services(query, filters)
        
        if not candidate_services:
            return []
        
        # Rank results
        ranked_results = self.ranker.rank_results(query, candidate_services)
        
        # Filter out low-relevance results (lower threshold for typo tolerance)
        filtered_results = [
            result for result in ranked_results
            if result['relevance_score'] > 0.05
        ]
        
        # Limit results
        return filtered_results[:self.max_results]
    
    def _get_candidate_services(self, query: str, filters: Optional[Dict[str, Any]] = None) -> QuerySet:
        """Get candidate services using database queries"""
        # Start with active services
        queryset = Service.objects.filter(is_active=True)

        # Apply basic text search using database
        query_words = query.lower().split()
        q_objects = Q()

        # First try exact word matches
        for word in query_words:
            word_q = (
                Q(name__icontains=word) |
                Q(description__icontains=word) |
                Q(provider__business_name__icontains=word) |
                Q(provider__business_description__icontains=word) |
                Q(category__name__icontains=word) |
                Q(provider__city__icontains=word)
            )
            q_objects |= word_q

        # If no exact matches found, try fuzzy matching with partial words
        initial_queryset = queryset.filter(q_objects)
        if not initial_queryset.exists() and len(query) > 3:
            # Try multiple fuzzy matching strategies for typo tolerance
            fuzzy_q_objects = Q()
            for word in query_words:
                if len(word) > 3:
                    # Strategy 1: Try partial matches (first 3-4 characters)
                    partial_word = word[:max(3, len(word)-1)]

                    # Strategy 2: Try removing last character (for extra letters)
                    truncated_word = word[:-1] if len(word) > 4 else word

                    # Strategy 3: Try first 3 characters for very different words
                    short_partial = word[:3] if len(word) > 3 else word

                    partial_q = (
                        Q(name__icontains=partial_word) |
                        Q(description__icontains=partial_word) |
                        Q(provider__business_name__icontains=partial_word) |
                        Q(category__name__icontains=partial_word) |
                        Q(name__icontains=truncated_word) |
                        Q(description__icontains=truncated_word) |
                        Q(name__icontains=short_partial) |
                        Q(category__name__icontains=short_partial)
                    )
                    fuzzy_q_objects |= partial_q

            if fuzzy_q_objects:
                queryset = queryset.filter(fuzzy_q_objects)
            else:
                queryset = initial_queryset
        else:
            queryset = initial_queryset

        # Apply additional filters if provided
        if filters:
            if 'category' in filters:
                queryset = queryset.filter(category__name__icontains=filters['category'])
            if 'city' in filters:
                queryset = queryset.filter(provider__city__icontains=filters['city'])
            if 'min_price' in filters:
                queryset = queryset.filter(base_price__gte=filters['min_price'])
            if 'max_price' in filters:
                queryset = queryset.filter(base_price__lte=filters['max_price'])

        # Optimize query with select_related and prefetch_related
        queryset = queryset.select_related('provider', 'category').distinct()

        return queryset
