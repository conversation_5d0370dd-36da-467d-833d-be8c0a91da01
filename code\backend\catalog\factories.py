"""
Data factories for generating realistic test data
Provides factory classes for creating test instances of models with realistic data
"""
import random
from decimal import Decimal
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timed<PERSON>ta

from catalog.models import ServiceCategory, ServiceProvider, Service

User = get_user_model()


class DataFactory:
    """Base factory class with common utilities"""
    
    @staticmethod
    def random_choice(choices):
        """Get random choice from a list"""
        return random.choice(choices)
    
    @staticmethod
    def random_decimal(min_val, max_val, decimal_places=2):
        """Generate random decimal within range"""
        return Decimal(str(round(random.uniform(min_val, max_val), decimal_places)))
    
    @staticmethod
    def random_phone():
        """Generate random Canadian phone number"""
        area_codes = ['416', '647', '437', '613', '343', '905', '289', '365']
        area_code = random.choice(area_codes)
        number = f"{random.randint(100, 999)}{random.randint(1000, 9999)}"
        return f"+1{area_code}{number}"
    
    @staticmethod
    def random_postal_code():
        """Generate random Canadian postal code"""
        letters = 'ABCEGHJKLMNPRSTVWXYZ'
        digits = '**********'
        return f"{random.choice(letters)}{random.choice(digits)}{random.choice(letters)} {random.choice(digits)}{random.choice(letters)}{random.choice(digits)}"


class CustomerFactory(DataFactory):
    """Factory for creating customer accounts"""
    
    FIRST_NAMES = [
        'Emma', 'Olivia', 'Ava', 'Isabella', 'Sophia', 'Charlotte', 'Mia', 'Amelia',
        'Harper', 'Evelyn', 'Abigail', 'Emily', 'Elizabeth', 'Mila', 'Ella', 'Avery',
        'Sofia', 'Camila', 'Aria', 'Scarlett', 'Victoria', 'Madison', 'Luna', 'Grace',
        'Chloe', 'Penelope', 'Layla', 'Riley', 'Zoey', 'Nora', 'Lily', 'Eleanor',
        'Hannah', 'Lillian', 'Addison', 'Aubrey', 'Ellie', 'Stella', 'Natalie', 'Zoe',
        'Leah', 'Hazel', 'Violet', 'Aurora', 'Savannah', 'Audrey', 'Brooklyn', 'Bella',
        'Claire', 'Skylar', 'Lucy', 'Paisley', 'Everly', 'Anna', 'Caroline', 'Nova',
        'Genesis', 'Emilia', 'Kennedy', 'Samantha', 'Maya', 'Willow', 'Kinsley', 'Naomi'
    ]
    
    LAST_NAMES = [
        'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis',
        'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson',
        'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin', 'Lee', 'Perez', 'Thompson',
        'White', 'Harris', 'Sanchez', 'Clark', 'Ramirez', 'Lewis', 'Robinson', 'Walker',
        'Young', 'Allen', 'King', 'Wright', 'Scott', 'Torres', 'Nguyen', 'Hill', 'Flores',
        'Green', 'Adams', 'Nelson', 'Baker', 'Hall', 'Rivera', 'Campbell', 'Mitchell',
        'Carter', 'Roberts', 'Gomez', 'Phillips', 'Evans', 'Turner', 'Diaz', 'Parker',
        'Cruz', 'Edwards', 'Collins', 'Reyes', 'Stewart', 'Morris', 'Morales', 'Murphy'
    ]
    
    @classmethod
    def create_customer(cls, **kwargs):
        """Create a customer with realistic data"""
        first_name = kwargs.get('first_name', cls.random_choice(cls.FIRST_NAMES))
        last_name = kwargs.get('last_name', cls.random_choice(cls.LAST_NAMES))

        # Generate unique email
        base_email = f"{first_name.lower()}.{last_name.lower()}@test.com"
        email = kwargs.get('email', base_email)

        # Ensure email uniqueness
        counter = 1
        while User.objects.filter(email=email).exists():
            email = f"{first_name.lower()}.{last_name.lower()}{counter}@test.com"
            counter += 1
        
        defaults = {
            'username': email,
            'email': email,
            'first_name': first_name,
            'last_name': last_name,
            'role': 'customer',
            'phone': cls.random_phone(),
            'is_active': True,
            'is_verified': True,
            'email_verified_at': timezone.now(),
            'is_test_account': True,
        }
        defaults.update(kwargs)
        
        return User.objects.create_user(password='TestPass123!', **defaults)


class ProviderFactory(DataFactory):
    """Factory for creating service provider accounts"""
    
    BUSINESS_NAMES = {
        'hair-beauty': [
            'Trendy Cuts & Color Studio', 'Glamour Hair Salon', 'Style & Grace Beauty',
            'The Hair Lounge', 'Chic Cuts Boutique', 'Luxe Hair Studio', 'Bella Beauty Bar',
            'Modern Mane Salon', 'Radiant Hair & Beauty', 'Elite Style Studio'
        ],
        'wellness-spa': [
            'Serenity Wellness Spa', 'Tranquil Touch Spa', 'Zen Garden Wellness',
            'Harmony Spa & Wellness', 'Blissful Retreat Spa', 'Pure Relaxation Center',
            'Oasis Wellness Studio', 'Peaceful Mind Spa', 'Rejuvenate Wellness', 'Calm Waters Spa'
        ],
        'fitness-training': [
            'Elite Personal Training', 'FitLife Training Studio', 'Peak Performance Gym',
            'Active Lifestyle Fitness', 'Strong Body Training', 'Vitality Fitness Center',
            'Dynamic Fitness Studio', 'PowerFit Training', 'Wellness Fitness Hub', 'Prime Fitness'
        ],
        'skincare-aesthetics': [
            'Glow Aesthetics Clinic', 'Radiant Skin Studio', 'Pure Beauty Aesthetics',
            'Luminous Skin Care', 'Flawless Aesthetics', 'Skin Renewal Clinic',
            'Beauty & Glow Center', 'Perfect Skin Studio', 'Youthful Glow Spa', 'Skin Perfection'
        ],
        'nail-care': [
            'Nail Artistry Studio', 'Perfect Polish Salon', 'Glamour Nails & Spa',
            'Chic Nail Boutique', 'Luxe Nail Lounge', 'Nail Perfection Studio',
            'Polished Beauty Bar', 'Nail Art Gallery', 'Elegant Nails Spa', 'Nail Harmony'
        ]
    }
    
    PROVIDER_NAMES = [
        ('Dr. Sarah', 'Kim'), ('Lisa', 'Zhang'), ('Maria', 'Rodriguez'), ('Nina', 'Patel'),
        ('Jessica', 'Chen'), ('Amanda', 'Wilson'), ('Rachel', 'Taylor'), ('Sophia', 'Martinez'),
        ('Emily', 'Johnson'), ('Ashley', 'Brown'), ('Michelle', 'Davis'), ('Jennifer', 'Garcia'),
        ('Stephanie', 'Miller'), ('Nicole', 'Anderson'), ('Melissa', 'Thomas'), ('Amy', 'Jackson'),
        ('Angela', 'White'), ('Heather', 'Harris'), ('Brenda', 'Martin'), ('Emma', 'Thompson')
    ]
    
    TORONTO_ADDRESSES = [
        ('123 Queen Street West', 'Toronto', 'M5H 2M9', 43.6532, -79.3832),
        ('456 King Street East', 'Toronto', 'M5A 1L6', 43.6514, -79.3598),
        ('789 Yonge Street', 'Toronto', 'M4W 2G8', 43.6629, -79.3957),
        ('321 Bay Street', 'Toronto', 'M5H 2R2', 43.6481, -79.3809),
        ('654 Bloor Street West', 'Toronto', 'M6G 1K4', 43.6677, -79.4103),
        ('987 College Street', 'Toronto', 'M6H 1A6', 43.6577, -79.4225),
        ('147 Spadina Avenue', 'Toronto', 'M5V 2L1', 43.6426, -79.3991),
        ('258 Dundas Street West', 'Toronto', 'M5T 1G5', 43.6563, -79.3904),
        ('369 Richmond Street West', 'Toronto', 'M5V 1X1', 43.6476, -79.3934),
        ('741 Adelaide Street West', 'Toronto', 'M5V 1P9', 43.6456, -79.3967)
    ]
    
    @classmethod
    def create_provider(cls, category_slug=None, **kwargs):
        """Create a service provider with realistic data"""
        # Get or create category
        if category_slug:
            try:
                category = ServiceCategory.objects.get(slug=category_slug)
            except ServiceCategory.DoesNotExist:
                category = ServiceCategory.objects.first()
        else:
            category = cls.random_choice(ServiceCategory.objects.all())
        
        if not category:
            raise ValueError("No service categories available. Create categories first.")
        
        # Generate business name
        business_names = cls.BUSINESS_NAMES.get(category.slug, cls.BUSINESS_NAMES['hair-beauty'])
        business_name = kwargs.get('business_name', cls.random_choice(business_names))
        
        # Generate provider name
        first_name, last_name = kwargs.get('name', cls.random_choice(cls.PROVIDER_NAMES))

        # Generate unique email
        base_email = f"{business_name.lower().replace(' ', '').replace('&', '').replace('.', '')}@test.com"
        email = kwargs.get('email', base_email)

        # Ensure email uniqueness
        counter = 1
        while User.objects.filter(email=email).exists():
            email = f"{business_name.lower().replace(' ', '').replace('&', '').replace('.', '')}{counter}@test.com"
            counter += 1
        
        # Create user account
        user = User.objects.create_user(
            username=email,
            email=email,
            password='TestPass123!',
            first_name=first_name,
            last_name=last_name,
            role='service_provider',
            phone=cls.random_phone(),
            is_active=True,
            is_verified=True,
            email_verified_at=timezone.now(),
            is_test_account=True
        )
        
        # Generate address
        address_data = cls.random_choice(cls.TORONTO_ADDRESSES)
        address, city, postal_code, lat, lng = address_data
        
        # Create provider profile
        provider_defaults = {
            'user': user,
            'business_name': business_name,
            'business_description': f"Professional {category.name.lower()} services in {city}",
            'business_phone': user.phone,
            'business_email': email,
            'address': address,
            'city': city,
            'state': 'Ontario',
            'zip_code': postal_code,
            'country': 'Canada',
            'latitude': Decimal(str(lat + random.uniform(-0.01, 0.01))),
            'longitude': Decimal(str(lng + random.uniform(-0.01, 0.01))),
            'is_verified': True,
            'is_active': True,
            'rating': cls.random_decimal(4.0, 5.0, 1),
            'review_count': random.randint(5, 100),
            'years_of_experience': random.randint(2, 20),
        }
        provider_defaults.update(kwargs)
        
        provider = ServiceProvider.objects.create(**provider_defaults)
        provider.categories.add(category)
        
        return provider


class ServiceFactory(DataFactory):
    """Factory for creating services"""
    
    SERVICE_TEMPLATES = {
        'hair-beauty': [
            {
                'name': 'Haircut & Style',
                'description': 'Professional haircut with styling and finishing',
                'base_price_range': (45, 85),
                'duration_range': (45, 75)
            },
            {
                'name': 'Hair Color',
                'description': 'Full hair coloring service with premium products',
                'base_price_range': (90, 150),
                'duration_range': (90, 150)
            },
            {
                'name': 'Highlights',
                'description': 'Professional hair highlighting with foil technique',
                'base_price_range': (75, 120),
                'duration_range': (75, 120)
            },
            {
                'name': 'Balayage',
                'description': 'Hand-painted hair highlighting technique for natural look',
                'base_price_range': (120, 200),
                'duration_range': (120, 180)
            },
            {
                'name': 'Hair Treatment',
                'description': 'Deep conditioning and repair treatment',
                'base_price_range': (35, 65),
                'duration_range': (30, 45)
            },
            {
                'name': 'Blowout',
                'description': 'Professional hair styling and blowout',
                'base_price_range': (25, 45),
                'duration_range': (30, 45)
            }
        ],
        'wellness-spa': [
            {
                'name': 'Relaxation Massage',
                'description': 'Full body relaxation massage with aromatherapy',
                'base_price_range': (70, 110),
                'duration_range': (60, 90)
            },
            {
                'name': 'Hot Stone Massage',
                'description': 'Therapeutic hot stone massage for deep relaxation',
                'base_price_range': (90, 130),
                'duration_range': (75, 90)
            },
            {
                'name': 'Facial Treatment',
                'description': 'Deep cleansing facial with customized skincare',
                'base_price_range': (60, 95),
                'duration_range': (60, 75)
            },
            {
                'name': 'Body Wrap',
                'description': 'Detoxifying body wrap treatment',
                'base_price_range': (80, 120),
                'duration_range': (60, 90)
            },
            {
                'name': 'Aromatherapy Session',
                'description': 'Relaxing aromatherapy treatment with essential oils',
                'base_price_range': (55, 85),
                'duration_range': (45, 60)
            }
        ],
        'fitness-training': [
            {
                'name': 'Personal Training Session',
                'description': 'One-on-one personal training with certified trainer',
                'base_price_range': (60, 100),
                'duration_range': (45, 60)
            },
            {
                'name': 'Group Fitness Class',
                'description': 'Small group fitness training session',
                'base_price_range': (20, 35),
                'duration_range': (45, 60)
            },
            {
                'name': 'Strength Training',
                'description': 'Focused strength and resistance training',
                'base_price_range': (70, 110),
                'duration_range': (60, 75)
            },
            {
                'name': 'Cardio Workout',
                'description': 'High-intensity cardio training session',
                'base_price_range': (50, 80),
                'duration_range': (45, 60)
            }
        ]
    }
    
    @classmethod
    def create_service(cls, provider, **kwargs):
        """Create a service for a provider"""
        category = provider.categories.first()
        if not category:
            raise ValueError("Provider must have at least one category")
        
        # Get service templates for category
        templates = cls.SERVICE_TEMPLATES.get(category.slug, cls.SERVICE_TEMPLATES['hair-beauty'])
        template = cls.random_choice(templates)
        
        # Generate pricing
        min_price, max_price = template['base_price_range']
        base_price = cls.random_decimal(min_price, max_price)
        
        # Generate duration
        min_duration, max_duration = template['duration_range']
        duration = random.randint(min_duration, max_duration)
        
        # Ensure unique service name for provider
        service_name = template['name']
        counter = 1
        while Service.objects.filter(provider=provider, name=service_name, is_active=True).exists():
            service_name = f"{template['name']} {counter}"
            counter += 1

        # Create service
        service_defaults = {
            'provider': provider,
            'category': category,
            'name': service_name,
            'description': template['description'],
            'base_price': base_price,
            'duration': duration,
            'buffer_time': random.randint(10, 20),
            'is_available': True,
            'is_active': True,
            'is_popular': random.choice([True, False, False, False]),  # 25% chance
            'booking_count': random.randint(0, 50)
        }
        service_defaults.update(kwargs)

        return Service.objects.create(**service_defaults)


class ServiceCategoryFactory(DataFactory):
    """Factory for creating service categories"""

    CATEGORIES = [
        {
            'name': 'Hair & Beauty',
            'slug': 'hair-beauty',
            'description': 'Professional hair styling, cutting, and beauty services',
            'icon': 'hair-dryer'
        },
        {
            'name': 'Spa & Wellness',
            'slug': 'wellness-spa',
            'description': 'Relaxation, massage, and wellness treatments',
            'icon': 'spa'
        },
        {
            'name': 'Fitness & Training',
            'slug': 'fitness-training',
            'description': 'Personal training and fitness services',
            'icon': 'dumbbell'
        },
        {
            'name': 'Skincare & Aesthetics',
            'slug': 'skincare-aesthetics',
            'description': 'Professional skincare and aesthetic treatments',
            'icon': 'face-cream'
        },
        {
            'name': 'Nail Care',
            'slug': 'nail-care',
            'description': 'Manicure, pedicure, and nail art services',
            'icon': 'nail-polish'
        }
    ]

    @classmethod
    def create_category(cls, **kwargs):
        """Create a service category"""
        if not kwargs:
            # Use a random category template
            template = cls.random_choice(cls.CATEGORIES)
            kwargs = template.copy()

        # Ensure unique slug
        slug = kwargs.get('slug', kwargs.get('name', '').lower().replace(' ', '-'))
        counter = 1
        original_slug = slug
        while ServiceCategory.objects.filter(slug=slug).exists():
            slug = f"{original_slug}-{counter}"
            counter += 1
        kwargs['slug'] = slug

        defaults = {
            'is_active': True,
            'sort_order': random.randint(1, 100)
        }
        defaults.update(kwargs)

        return ServiceCategory.objects.create(**defaults)

    @classmethod
    def create_batch(cls, size=5):
        """Create multiple categories"""
        categories = []
        for i in range(min(size, len(cls.CATEGORIES))):
            template = cls.CATEGORIES[i].copy()
            # Make name unique by adding index
            template['name'] = f"{template['name']} {i+1}"
            template['slug'] = f"{template['slug']}-{i+1}"
            category = cls.create_category(**template)
            categories.append(category)
        return categories
