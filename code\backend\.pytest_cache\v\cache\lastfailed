{"authentication/tests.py": true, "debug_test.py::test_registration": true, "test_consolidated_accounts.py::ConsolidatedTestAccountsTest::test_all_accounts_database_consistency": true, "test_consolidated_accounts.py::ConsolidatedTestAccountsTest::test_email_not_verified_accounts": true, "test_consolidated_accounts.py::ConsolidatedTestAccountsTest::test_invalid_credentials_accounts": true, "test_consolidated_accounts.py::TestAccountPasswordValidationTest::test_password_strength_requirements": true, "test_consolidated_accounts.py::TestAccountAPIEndpointsTest::test_login_endpoint_with_all_accounts": true, "test_postgresql_config.py::PostgreSQLConnectionTest::test_database_settings_configuration": true, "test_postgresql_config.py::PostgreSQLConnectionTest::test_django_database_connection": true, "test_postgresql_config.py::PostgreSQLConnectionTest::test_postgresql_connection_available": true, "test_postgresql_config.py::PostgreSQLMigrationTest::test_authentication_tables_exist": true, "test_postgresql_config.py::PostgreSQLMigrationTest::test_catalog_tables_exist": true, "test_postgresql_config.py::PostgreSQLDataIntegrityTest::test_foreign_key_relationships": true, "test_postgresql_config.py::PostgreSQLPerformanceTest::test_database_indexes_exist": true, "test_postgresql_config.py::PostgreSQLEnvironmentTest::test_postgresql_version_compatibility": true, "test_audit_database_config.py::DatabaseConfigurationAuditTest::test_database_settings_structure_matches_reference": true, "test_audit_database_config.py::DatabaseConfigurationAuditTest::test_postgresql_configuration_matches_reference": true, "test_database_config_verification.py::DatabaseConfigurationVerificationTest::test_environment_based_settings_structure_exists": true, "test_database_config_verification.py::DatabaseConfigurationVerificationTest::test_environment_variable_configuration": true, "test_database_config_verification.py::DatabaseConfigurationVerificationTest::test_postgresql_configuration_structure_when_available": true, "test_database_config_verification.py::DatabaseConfigurationVerificationTest::test_testing_database_configuration": true, "authentication/test_acceptance.py::TestCase": true, "authentication/test_acceptance.py::TransactionTestCase": true, "authentication/test_acceptance.py::APITestCase": true, "authentication/test_acceptance.py::UserRegistrationAcceptanceTests": true, "authentication/test_acceptance.py::UserLoginAcceptanceTests": true, "authentication/test_acceptance.py::EmailVerificationAcceptanceTests": true, "authentication/test_acceptance.py::SocialAuthenticationAcceptanceTests": true, "authentication/test_login_fix.py::TestCase": true, "authentication/test_login_fix.py::APITestCase": true, "authentication/test_login_fix.py::LoginAuthenticationFixTests": true, "authentication/test_login_fix.py::LoginIntegrationTests": true, "authentication/test_urls.py::TestCase": true, "authentication/test_urls.py::APITestCase": true, "authentication/test_urls.py::AuthenticationURLTests": true, "authentication/test_urls.py::AuthenticationURLAccessTests": true, "authentication/tests/test_login_authentication.py::TestCase": true, "authentication/tests/test_login_authentication.py::APITestCase": true, "authentication/tests/test_login_authentication.py::LoginAuthenticationViewTests": true, "authentication/tests/test_login_authentication.py::AccountLockoutTests": true, "authentication/tests/test_login_authentication.py::LoginSerializerTests": true, "authentication/tests/test_login_authentication.py::JWTTokenGenerationTests": true, "authentication/tests/test_login_authentication.py::RateLimitingTests": true, "authentication/tests/test_login_authentication.py::ErrorResponseConsistencyTests": true, "catalog/tests/test_account_management.py::TestCase": true, "catalog/tests/test_account_management.py::TestAccountSecurity": true, "catalog/tests/test_account_management.py::TestAccountManager": true, "catalog/tests/test_account_management.py::TestAccountCreationTest": true, "catalog/tests/test_account_management.py::TestAccountCleanupTest": true, "catalog/tests/test_account_management.py::TestAccountSecurityTest": true, "catalog/tests/test_account_management.py::SampleDataGenerationTest": true, "catalog/tests/test_account_management.py::DataVerificationTest": true, "catalog/tests/test_account_management.py::SecurityAuditTest": true, "catalog/tests/test_provider_service_api_integration.py::TestCase": true, "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration": true, "catalog/tests/test_account_management.py::TestAccountSecurityTest::test_environment_detection_production": true, "test_allowed_hosts.py::AllowedHostsConfigurationTest::test_127_0_0_1_allowed": true, "test_allowed_hosts.py::AllowedHostsConfigurationTest::test_android_emulator_ip_allowed": true, "test_allowed_hosts.py::AllowedHostsConfigurationTest::test_localhost_allowed": true, "test_allowed_hosts.py::AllowedHostsConfigurationTest::test_network_ip_192_168_2_65_allowed": true, "test_allowed_hosts.py::AllowedHostsConfigurationTest::test_request_from_localhost_accepted": true, "test_allowed_hosts.py::AllowedHostsConfigurationTest::test_request_from_network_ip_accepted": true, "test_postgresql_config.py::PostgreSQLEnvironmentTest::test_sqlite_fallback_disabled": true, "catalog/tests/test_advanced_search_algorithm.py": true}